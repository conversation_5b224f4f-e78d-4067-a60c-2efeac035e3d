/**
 * 语音服务验证工具
 * 用于验证当前使用的语音识别服务类型
 */

import { SPEECH_RECOGNITION_CONFIG } from '@/constants/constant';
import { SpeechRecognitionFactory } from './speechRecognitionFactory';

export class SpeechServiceValidator {
  /**
   * 验证当前配置的语音服务类型
   */
  static validateCurrentService(): {
    serviceType: string;
    isCustomService: boolean;
    wsUrl?: string;
    isAvailable: boolean;
    message: string;
  } {
    const serviceType = SPEECH_RECOGNITION_CONFIG.TYPE;
    const isCustomService = serviceType === 'custom';
    const isAvailable = SpeechRecognitionFactory.isTypeAvailable(serviceType);
    
    let message = '';
    let wsUrl: string | undefined;
    
    if (isCustomService) {
      wsUrl = SPEECH_RECOGNITION_CONFIG.CUSTOM.WS_URL;
      message = `✅ 当前使用自定义语音识别服务\n🔗 WebSocket URL: ${wsUrl}`;
    } else {
      message = `🔵 当前使用阿里云语音识别服务`;
    }
    
    if (!isAvailable) {
      message += `\n⚠️ 警告: 服务不可用`;
    }
    
    return {
      serviceType,
      isCustomService,
      wsUrl,
      isAvailable,
      message
    };
  }

  /**
   * 测试语音服务连接
   */
  static async testServiceConnection(): Promise<{
    success: boolean;
    message: string;
    details?: string;
  }> {
    try {
      const validation = this.validateCurrentService();
      
      if (!validation.isAvailable) {
        return {
          success: false,
          message: '服务不可用',
          details: '当前配置的语音识别服务不可用'
        };
      }

      // 创建语音识别实例进行测试
      const speechRecognition = SpeechRecognitionFactory.createSpeechRecognition();
      
      // 测试配置
      const config = validation.isCustomService 
        ? { wsUrl: validation.wsUrl }
        : { appKey: 'test-key', token: 'test-token' };

      await speechRecognition.initialize(config, {
        onConnect: () => {
          console.log('[Validator] 连接成功');
        },
        onError: (error) => {
          console.error('[Validator] 连接错误:', error);
        }
      });

      return {
        success: true,
        message: '服务配置正确',
        details: validation.message
      };

    } catch (error: any) {
      return {
        success: false,
        message: '服务测试失败',
        details: error.message || '未知错误'
      };
    }
  }

  /**
   * 在控制台输出当前服务状态
   */
  static logCurrentServiceStatus(): void {
    const validation = this.validateCurrentService();
    
    console.group('🎤 语音识别服务状态');
    console.log('服务类型:', validation.serviceType);
    console.log('是否为自定义服务:', validation.isCustomService);
    if (validation.wsUrl) {
      console.log('WebSocket URL:', validation.wsUrl);
    }
    console.log('服务可用性:', validation.isAvailable ? '✅ 可用' : '❌ 不可用');
    console.log('详细信息:', validation.message);
    console.groupEnd();
  }
}

// 自动在开发环境下输出服务状态
if (import.meta.env.DEV) {
  SpeechServiceValidator.logCurrentServiceStatus();
}
