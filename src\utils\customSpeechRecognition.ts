/**
 * 自定义语音识别工具类
 * 基于自定义语音识别服务实现实时语音转文字功能
 * 兼容阿里云语音识别接口，可无缝替换
 */

export interface CustomSpeechConfig {
  wsUrl: string;
  token?: string;
}

export interface SpeechRecognitionResult {
  text: string;
  isFinal: boolean;
  confidence?: number;
  timestamp?: number;
}

export interface SpeechRecognitionCallbacks {
  onResult?: (result: SpeechRecognitionResult) => void;
  onError?: (error: Error) => void;
  onStart?: () => void;
  onEnd?: () => void;
  onConnect?: () => void;
  onDisconnect?: (code: number, reason: string) => void;
}

class CustomSpeechRecognition {
  private ws: WebSocket | null = null;
  private config: CustomSpeechConfig | null = null;
  private callbacks: SpeechRecognitionCallbacks = {};
  private isConnected = false;
  private isRecognitionStarted = false;
  private chunkSize = 960; // 音频块大小，参考示例代码
  private sampleBuffer = new Int16Array();

  /**
   * 初始化语音识别
   * @param config 配置信息
   * @param callbacks 回调函数
   */
  async initialize(config: CustomSpeechConfig, callbacks: SpeechRecognitionCallbacks = {}) {
    this.config = config;
    this.callbacks = callbacks;
    
    if (!config.wsUrl) {
      throw new Error('自定义语音识别配置不完整：缺少 wsUrl');
    }
  }

  /**
   * 连接WebSocket
   */
  async connect(): Promise<void> {
    if (this.isConnected && this.ws) {
      return;
    }

    return new Promise((resolve, reject) => {
      try {
        const { wsUrl } = this.config!;
        
        this.ws = new WebSocket(wsUrl);

        this.ws.onopen = () => {
          console.log('[CustomSR] WebSocket连接已建立');
          this.isConnected = true;
          
          // 发送初始化配置消息，参考示例代码
          const initMessage = {
            chunk_size: [5, 10, 5],
            wav_name: "vue_app",
            is_speaking: true,
            chunk_interval: 10,
            itn: true, // 逆文本标准化
            mode: "2pass" // 使用2pass模式
          };
          
          this.sendMessage(JSON.stringify(initMessage));
          console.log('[CustomSR] 已发送初始化消息');
          
          this.callbacks.onConnect?.();
          resolve();
        };

        this.ws.onmessage = (event) => {
          this.handleMessage(event.data);
        };

        this.ws.onclose = (event) => {
          console.log('[CustomSR] WebSocket连接已关闭:', event.code, event.reason);
          this.isConnected = false;
          this.isRecognitionStarted = false;
          this.callbacks.onDisconnect?.(event.code, event.reason);
        };

        this.ws.onerror = (error) => {
          console.error('[CustomSR] WebSocket错误:', error);
          this.isConnected = false;
          this.isRecognitionStarted = false;
          reject(new Error('WebSocket连接失败'));
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 开始语音识别
   */
  async startRecognition(): Promise<void> {
    if (!this.isConnected || !this.ws) {
      throw new Error('WebSocket未连接');
    }

    if (this.isRecognitionStarted) {
      return;
    }

    this.isRecognitionStarted = true;
    this.sampleBuffer = new Int16Array();
    this.callbacks.onStart?.();
    console.log('[CustomSR] 语音识别已开始');
  }

  /**
   * 发送音频数据
   * @param audioData 音频数据buffer
   */
  sendAudioData(audioData: ArrayBuffer): void {
    if (!this.isConnected || !this.ws || !this.isRecognitionStarted) {
      console.warn('[CustomSR] WebSocket未连接或识别未开始，无法发送音频数据');
      return;
    }

    if (this.ws.readyState === WebSocket.OPEN) {
      // 将音频数据转换为Int16Array并添加到缓冲区
      const int16Data = new Int16Array(audioData);
      const newBuffer = new Int16Array(this.sampleBuffer.length + int16Data.length);
      newBuffer.set(this.sampleBuffer);
      newBuffer.set(int16Data, this.sampleBuffer.length);
      this.sampleBuffer = newBuffer;

      // 按块发送数据
      while (this.sampleBuffer.length >= this.chunkSize) {
        const chunk = this.sampleBuffer.slice(0, this.chunkSize);
        this.sampleBuffer = this.sampleBuffer.slice(this.chunkSize);
        this.ws.send(chunk);
      }
    }
  }

  /**
   * 停止语音识别
   */
  async stopRecognition(): Promise<void> {
    if (!this.isConnected || !this.ws || !this.isRecognitionStarted) {
      return;
    }

    // 发送剩余的音频数据
    if (this.sampleBuffer.length > 0) {
      this.ws.send(this.sampleBuffer);
      this.sampleBuffer = new Int16Array();
    }

    // 发送停止消息，参考示例代码
    const stopMessage = {
      chunk_size: [5, 10, 5],
      wav_name: "vue_app",
      is_speaking: false,
      chunk_interval: 10,
      mode: "2pass"
    };

    this.sendMessage(JSON.stringify(stopMessage));
    this.isRecognitionStarted = false;
    this.callbacks.onEnd?.();
    console.log('[CustomSR] 停止语音识别');
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    if (this.ws) {
      this.ws.close(1000, '客户端主动断开');
      this.ws = null;
      this.isConnected = false;
      this.isRecognitionStarted = false;
      this.sampleBuffer = new Int16Array();
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleMessage(data: string): void {
    try {
      const message = JSON.parse(data);
      console.log('[CustomSR] 收到消息:', message);

      // 处理识别结果，参考示例代码中的消息格式
      if (message.text !== undefined) {
        const result: SpeechRecognitionResult = {
          text: message.text,
          isFinal: message.is_final === true,
          confidence: message.confidence,
          timestamp: Date.now()
        };
        this.callbacks.onResult?.(result);
      }
    } catch (error) {
      console.warn('[CustomSR] 解析消息失败:', data);
      console.error(error);
    }
  }

  /**
   * 发送JSON消息
   */
  private sendMessage(message: string): void {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(message);
      console.log('[CustomSR] 发送消息:', message);
    } else {
      console.warn('[CustomSR] WebSocket未开启，无法发送消息');
    }
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): boolean {
    return this.isConnected;
  }

  /**
   * 获取当前任务ID（兼容接口）
   */
  getCurrentTaskId(): string {
    return 'custom-speech-recognition';
  }
}

export default CustomSpeechRecognition;
