import AiAssistantIcon from "@/components/icons/AiAssistantIcon.vue";
import DiagnosisIcon from "@/components/icons/DiagnosisIcon.vue";
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import VoiceRecordingIcon from "@/components/icons/VoiceRecordingIcon.vue";

export type FunctionItem = {
  id: string;
  title: string;
  icon: any;
  subTitle: string;
  description: string;
};

export const FUNCTION_LIST: FunctionItem[] = [
  {
    id: "chat",
    title: "辅助鉴别诊断",
    icon: DiagnosisIcon,
    subTitle: "好兽医AI助手",
    description:
      "无论是宠物健康咨询、医疗建议还是闲聊解压，都可以随时告诉我哦！今天有什么可以帮你的吗？",
  },
  {
    id: "knowledge",
    title: "AI知识助手",
    icon: AiAssistantIcon,
    subTitle: "AI知识助手",
    description:
      "无论是疾病查询、用药指导，还是健康科普，我都在行哦！今天想了解什么医疗知识呢？",
  },
  {
    id: "voice",
    title: "语音病历录入",
    icon: VoiceRecordingIcon,
    subTitle: "语音病历录入助手",
    description:
      "只需说出症状或检查结果，我将为您快速生成规范病历！现在开始吗？",
  },
  {
    id: "function_4",
    title: "影像诊断助手",
    icon: ImageDiagnosisIcon,
    subTitle: "影像诊断助手",
    description:
      "请上传您的检查资料，我将为您提供详细的解读参考！今天需要帮您分析医学影像报告吗？",
  },
];

// 会话相关常量
export const CONVERSATION_TITLE = "宠物健康助手";
export const CONVERSATION_TYPE = 1;

// 登录相关
export const AREA_CODE = [
  {
    key: "cn",
    value: "+86",
    label: "中国",
  },
  {
    key: "hk",
    value: "+852",
    label: "中国香港",
  },
  {
    key: "am",
    value: "+853",
    label: "中国澳门",
  },
  {
    key: "tw",
    value: "+886",
    label: "中国台湾",
  },
];

// 不喜欢反馈类别
export type DislikeCategory = {
  type: number;
  label: string;
  image: string;
};

export const DISLIKE_CATEGORIES: DislikeCategory[] = [
  { type: 1, label: "有害/不安全", image: "/chat/dislike_type_1.jfif" },
  { type: 2, label: "虚假信息", image: "/chat/dislike_type_2.jpeg" },
  { type: 3, label: "没有帮助", image: "/chat/dislike_type_3.jpeg" },
  { type: 4, label: "其他", image: "/chat/dislike_type_4.jfif" },
];

// 语音识别配置
export type SpeechRecognitionType = 'aliyun' | 'custom';

export const SPEECH_RECOGNITION_CONFIG = {
  // 语音识别类型：'aliyun' 使用阿里云服务，'custom' 使用自定义服务
  TYPE: 'custom' as SpeechRecognitionType,

  // 阿里云语音识别服务配置
  ALIYUN: {
    // WebSocket服务地址
    WS_URL: 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1',
    // 音频格式配置
    AUDIO_FORMAT: {
      format: 'pcm',
      sample_rate: 16000,
      enable_intermediate_result: true,
      enable_punctuation_prediction: true,
      enable_inverse_text_normalization: true
    },
    // Token缓存配置
    TOKEN_CACHE: {
      CACHE_KEY: 'aliyun_speech_token',
      BUFFER_TIME: 5 * 60 * 1000, // 5分钟缓冲时间
    },
    // WebSocket消息命名空间
    NAMESPACE: 'SpeechTranscriber',
    // 支持的消息类型
    MESSAGE_TYPES: {
      START_TRANSCRIPTION: 'StartTranscription',
      STOP_TRANSCRIPTION: 'StopTranscription',
      TRANSCRIPTION_STARTED: 'TranscriptionStarted',
      TRANSCRIPTION_RESULT_CHANGED: 'TranscriptionResultChanged',
      TRANSCRIPTION_COMPLETED: 'TranscriptionCompleted'
    }
  },

  // 自定义语音识别服务配置
  CUSTOM: {
    WS_URL: 'ws://***********:8000/ws/voice?token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxIiwiZXhwIjoxNzU3NjcxNDc0LCJ0eXBlIjoiYWNjZXNzIn0.KV8K9CsJLJ2CU9Jitr89gDS8zuNNTIbhUKNGSyPmHHA',
    // 自定义服务音频配置
    AUDIO_CONFIG: {
      chunk_size: [5, 10, 5],
      wav_name: "vue_app",
      is_speaking: true,
      chunk_interval: 10,
      itn: true, // 逆文本标准化
      mode: "2pass" // 使用2pass模式
    }
  }
};
