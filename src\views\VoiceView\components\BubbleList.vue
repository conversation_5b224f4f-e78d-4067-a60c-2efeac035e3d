<script lang="ts" setup>
import CollapseIcon from "@/components/icons/CollapseIcon.vue";
import CopyIcon from "@/components/icons/CopyIcon.vue";
import DislikeFilledIcon from "@/components/icons/DislikeFilledIcon.vue";
import DislikeIcon from "@/components/icons/DislikeIcon.vue";
import ExpandIcon from "@/components/icons/ExpandIcon.vue";
import LikeFilledIcon from "@/components/icons/LikeFilledIcon.vue";
import LikeIcon from "@/components/icons/LikeIcon.vue";
import { useChatStore } from "@/stores/chat";
import { useSliderStore } from "@/stores/slider";
import Dislike from "@/views/ChatView/components/Dislike.vue";
import { ElMessage } from "element-plus";
import { throttle } from "lodash-es";
import { storeToRefs } from "pinia";
import { computed, nextTick, ref } from "vue";

// 全局的Pina库
const chatStore = useChatStore();
const sliderStore = useSliderStore();

// 全局的响应数据
const { messageList, messagePagination, messageLoading } = storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);

//本组件的响应数据
const collapsedThinking = ref<Record<number, boolean>>({});
const showDislikeFeedbackDialog = ref<boolean>(false);
const currentDislikeMessageId = ref<number | null>(null);
const bubbleListRef = ref<HTMLElement | null>(null);
const savedScrollPosition = ref<number>(0);
const savedScrollHeight = ref<number>(0);
const bubbleList = computed(() => {
  return messageList.value.map((item) => {
    return {
      ...item,
      key: item.id,
      role: item.type,
      placement: item.type === "ai" ? "start" : "end",
      content: item.content,
      thinking: item.thinking || "",
      loading: item.thinkLoading,
      finished: !item.loading,
      isMarkdown: item.type === "ai",
      avatar: item.type === "ai" ? "logo.png" : "",
      avatarSize: "48px",
    };
  });
});
const hasMoreMessages = computed(() => {
  const page = messagePagination.value.page;
  const pages = messagePagination.value.pages;
  return page && pages ? page < pages : true;
});

//本组件的方法
const toggleThinking = (messageId: number) => {
  collapsedThinking.value[messageId] = !collapsedThinking.value[messageId];
};
const copyMessage = async (messageContent: string) => {
  try {
    // 优先使用 Clipboard API（需要 HTTPS 或 localhost）
    if (navigator.clipboard && window.isSecureContext) {
      await navigator.clipboard.writeText(messageContent);
      ElMessage.success("已复制到剪贴板");
      return;
    }
    
    // 降级方案：使用传统的 document.execCommand（兼容 HTTP）
    const textArea = document.createElement('textarea');
    textArea.value = messageContent;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();
    
    const successful = document.execCommand('copy');
    document.body.removeChild(textArea);
    
    if (successful) {
      ElMessage.success("已复制到剪贴板");
    } else {
      throw new Error('execCommand copy failed');
    }
  } catch (error) {
    ElMessage.error("复制失败");
    console.error('复制失败:', error);
  }
};
const likeMessage = async (messageId: number) => {
  await chatStore.likeMessage(messageId);
  ElMessage.success("感谢您的反馈！");
};
const unlikeMessage = async (messageId: number) => {
  await chatStore.cancelLikeMessage(messageId);
  ElMessage.success("已取消点赞");
};

const undislikeMessage = async (messageId: number) => {
  await chatStore.cancelDislikeMessage(messageId);
  ElMessage.success("已取消踩");
};

const handleShowDislikeFeedback = (messageId: number) => {
  currentDislikeMessageId.value = messageId;
  showDislikeFeedbackDialog.value = true;
};

const handleCloseDislikeFeedback = () => {
  currentDislikeMessageId.value = null;
  showDislikeFeedbackDialog.value = false;
};

const loadMoreMessages = throttle(
  async () => {
    if (!curConversation.value?.conversation_id) return;
    if (!bubbleListRef.value) return;
    // 记录加载前的滚动位置和内容高度
    const scrollElement = bubbleListRef.value.querySelector('.el-bubble-list') as HTMLElement;
    if (!scrollElement) return;
    savedScrollPosition.value = scrollElement.scrollTop;
    savedScrollHeight.value = scrollElement.scrollHeight;
    const curPage = chatStore.messagePagination.page + 1;
    await chatStore.loadMoreMessageList(
      curConversation.value.conversation_id,
      curPage
    );
    // 等待DOM更新后恢复滚动位置
    await nextTick();
    restoreScrollPosition();
  },
  2000,
  {
    leading: false,
    trailing: true,
  }
);
const bubbleListScroll = (event: Event) => {
  const target = event.target as HTMLElement;
  if (target?.scrollTop === 0 && hasMoreMessages.value && !messageLoading.value) {
    loadMoreMessages();
  }
};
const restoreScrollPosition = (): void => {
  if (!bubbleListRef.value) return;
  const scrollElement = bubbleListRef.value.querySelector('.el-bubble-list') as HTMLElement;
  if (!scrollElement) return;
  // 计算新内容的高度差
  const newScrollHeight = scrollElement.scrollHeight;
  const heightDifference = newScrollHeight - savedScrollHeight.value;
  // 恢复到加载前的相对位置（加上新增内容的高度）
  const newScrollTop = savedScrollPosition.value + heightDifference;
  scrollElement.scrollTop = newScrollTop;
  // 重置保存的位置
  savedScrollPosition.value = 0;
  savedScrollHeight.value = 0;
};
</script>

<template>
  <div class="bubble-list-container" ref="bubbleListRef" :class="{ 'have-more-message': hasMoreMessages }">
    <BubbleList :list="bubbleList" max-height="90dvh" class="bubble-list" @scroll="bubbleListScroll"
      :backButtonPosition="{ bottom: '20dvh', left: 'calc(50% - 19px)' }">
      <template #header="{ item }">
        <el-card v-if="item.thinking" class="thinking-container"
          :class="{ 'thinking-collapsed': collapsedThinking[item.id] }" header-class="thinking-header"
          body-class="thinking-body" shadow="never">
          <template #header>
            <div class="thinking-header-content">
              <span style="padding-right: 24px">{{
                item.thinkingLoading ? "深度思考中..." : "已经思考完毕"
                }}</span>
              <el-button v-if="!item.loading" size="small" class="thinking-collapse-button"
                @click="toggleThinking(item.id)">
                <ExpandIcon v-if="collapsedThinking[item.id]" />
                <CollapseIcon v-else />
              </el-button>
            </div>
          </template>
          <transition name="thinking-collapse">
            <div v-show="!collapsedThinking[item.id]" class="thinking-content"
              :class="{ 'thinking-loading': item.thinkingLoading }">
              {{
                item.thinking || (item.thinkingLoading ? "正在深度思考..." : "")
              }}
            </div>
          </transition>
        </el-card>
      </template>
      <template #footer="{ item }">
        <div class="footer-container" v-if="item.finished">
          <div class="footer-buttons" v-if="item.role === 'ai' && item.content">
            <el-button size="small" text @click="copyMessage(item.content)" class="footer-button copy-button"
              title="复制">
              <CopyIcon />
            </el-button>
            <el-button v-if="!item.isLike" size="small" text @click="likeMessage(item.id)"
              class="footer-button like-button" title="点赞">
              <LikeIcon />
            </el-button>
            <el-button v-else size="small" text @click="unlikeMessage(item.id)" class="footer-button like-button liked"
              title="取消点赞">
              <LikeFilledIcon />
            </el-button>
            <el-button v-if="!item.isDislike" size="small" text @click="handleShowDislikeFeedback(item.id)"
              class="footer-button dislike-button" title="踩">
              <DislikeIcon />
            </el-button>
            <el-button v-else size="small" text @click="undislikeMessage(item.id)"
              class="footer-button dislike-button disliked" title="取消踩">
              <DislikeFilledIcon />
            </el-button>
          </div>
          <div class="footer-remarks" v-if="item.role === 'ai'">
            本回答由 AI 生成，内容仅供参考，请仔细甄别
          </div>
        </div>
      </template>
    </BubbleList>
    <!-- 不喜欢反馈对话框 -->
    <Dislike v-model:visible="showDislikeFeedbackDialog" :message-id="currentDislikeMessageId"
      @close="handleCloseDislikeFeedback" />
  </div>
</template>
<style scoped src="./BubbleList.css"></style>
