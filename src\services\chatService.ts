import { useCommonStore } from "@/stores/common";
import { TokenCookieManager } from "@/utils/cookieUtils";
import { fetchEventSource } from "@microsoft/fetch-event-source";
import api from "../utils/api";
import type { BookInfo } from "./knowledgeService";

//图片附件
export interface ImageAttachment {
  id: number;
  uid: number;
  name: string;
  size: number;
  response: {
    base64: string;
    ocrText?: string;
    tableContent?: string;      // 表格内容
    nonTableContent?: string;   // 非表格内容（宠物信息等）
  };
  raw: File;
  percentage: number;
  status: "uploading" | "success" | "error";
  error?: string;
}

// 聊天消息类型
export interface ChatMessage {
  id: number;
  type: "user" | "ai" | string;
  content: string;
  thinking?: string;
  timestamp?: number;
  loading?: boolean;
  thinkLoading?: boolean;
  isLike?: boolean;
  isDislike?: boolean;
  uploadResultImages?: ImageAttachment[];
  uploadReportImages?: ImageAttachment[];
  books?: BookInfo[];
}

// 发送消息的请求参数
export interface SendMessageRequest {
  message: string;
  conversationId: string;
  uploadResultImages?: ImageAttachment[];
  uploadReportImages?: ImageAttachment[];
}

// 流式响应的数据块
export interface StreamChunk {
  content: string;
  thinking: string;
  finished: boolean;
  thinkFinished: boolean;
  messageId: number;
}

/**
 * 聊天服务类
 */
export class ChatService {
  // 用于中止请求的控制器
  private static abortController: AbortController | null = null;
  // 重试计数器
  private static retryCount: number = 0;
  // 最大重试次数
  private static readonly MAX_RETRIES = 3;

  /**
   * 中止当前的流式请求
   */
  static abortCurrentRequest(): void {
    if (this.abortController) {
      this.abortController.abort();
      this.abortController = null;
    }
  }

  /**
   * 创建新会话
   */
  static async createConversation(): Promise<string> {
    try {
      const response: any = await api.post("/api/conversations/", {
        title: "宠物健康助手",
        conversation_type: 1,
      });
      return response.conversation_id || response.id;
    } catch (error) {
      console.error("创建会话失败:", error);
      throw error;
    }
  }

  /**
   * 发送消息并获取流式响应
   * 这个方法用于实现打字机效果
   */
  static async sendMessageStream(
    params: SendMessageRequest,
    onChunk: (chunk: StreamChunk) => void,
    onStart: (messageId: number) => void
  ): Promise<void> {
    const { message, uploadResultImages, uploadReportImages } = params;
    let messageId: number;
    // 创建新的中止控制器
    this.abortController = new AbortController();
    // 重置重试计数器
    this.retryCount = 0;

    let totalContent = "";
    let totalThinking = "";
    let thinkFinished = false;

    //处理附件信息
    let uploadReportMessage = "";
    if (uploadReportImages && uploadReportImages.length > 0) {
      const uploadReportTexts = uploadReportImages
        .filter((att) => !!att.response.ocrText)
        .map((att) => `影像报告ocr识别内容：${att.response.ocrText}`)
        .join("\n");
      if (uploadReportTexts) {
        uploadReportMessage = `${uploadReportTexts}\n\n`;
      }
    }

    //处理附件信息
    let uploadResultMessage = "";
    if (uploadResultImages && uploadResultImages.length > 0) {
      const uploadResultTexts = uploadResultImages
        .filter((att) => !!att.response.ocrText)
        .map((att) => `化验结果ocr识别内容：${att.response.ocrText}`)
        .join("\n");
      if (uploadResultTexts) {
        uploadResultMessage = `${uploadResultTexts}\n\n`;
      }
    }

    try {
      await fetchEventSource("/api/system-agents/diagnosis", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "text/event-stream",
          Authorization: "Bearer " + TokenCookieManager.getToken()!,
        },
        openWhenHidden:true,
        signal: this.abortController.signal,
        body: JSON.stringify({
          messages: [
            {
              role: "user",
              content:
                uploadResultMessage +
                uploadReportMessage +
                (message ?? "根据化验结果或影响报告进行分析"),
            },
          ],
          stream: true,
          conversation_id: params.conversationId,
        }),
        async onopen(response) {
          if (response.ok) {
            // 连接成功，重置重试计数器
            ChatService.retryCount = 0;
            return; // 一切正常
          } else if (
            response.status >= 400 &&
            response.status < 500 &&
            response.status !== 429
          ) {
            // 客户端错误，不重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          } else {
            // 服务器错误或其他问题，可能会重试
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
          }
        },
        onmessage(event) {
          if (event.data === "[DONE]") {
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              thinkFinished: true,
              finished: true,
              messageId: messageId,
            });
            return;
          }
          const jsonData = JSON.parse(event.data);
          if (jsonData.event === "start") {
            messageId = jsonData.user_message_id;
            onStart(messageId);
          }
          // 检查是否是结束标记
          if (jsonData.event === "end") {
            return;
          }
          // 根据实际的 API 响应格式提取内容
          let content = "";

          if (jsonData.content) {
            content = jsonData.content;
          }

          if (content) {
            if (content === "</think>") {
              thinkFinished = true;
              onChunk({
                content: totalContent,
                thinking: totalThinking,
                thinkFinished: true,
                finished: false,
                messageId: messageId,
              });
              return;
            }
            if (thinkFinished) {
              totalContent += content;
            } else {
              totalThinking += content;
            }

            // 发送增量更新
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              thinkFinished: thinkFinished,
              finished: false,
              messageId: messageId,
            });
          }
        },
        onerror(error) {
          const httpCode: number = parseInt(
            error.message.split(":")[0].replace("HTTP", "").trim()
          );
          console.error(
            "SSE连接错误:",
            error,
            "重试次数:",
            ChatService.retryCount
          );
          // 检查重试次数
          if (ChatService.retryCount >= ChatService.MAX_RETRIES) {
            console.error("已达到最大重试次数，停止重试");
            throw new Error("连接失败，请稍后重试");
          }

          if (httpCode >= 400 && httpCode < 500 && httpCode !== 429) {
            if (httpCode === 401) {
              //Pina库
              const commonStore = useCommonStore();
              commonStore.showLogin = true;
            }
            throw error;
          } else if (isNaN(httpCode)) {
            throw new Error("处理错误，请稍候重试");
          }
          ChatService.retryCount++;
        },
        onclose() {
          // 连接关闭时确保发送最终完成状态
          if (totalContent || totalThinking) {
            onChunk({
              content: totalContent,
              thinking: totalThinking,
              finished: true,
              thinkFinished: true,
              messageId: Date.now(),
            });
          }
        },
      });
    } finally {
      // 清理中止控制器
      this.abortController = null;
    }
  }

  /**
   * 点赞消息
   * @param messageId 消息ID
   */
  static async likeMessageFromApi(messageId: number): Promise<any> {
    return await api.post(`api/message_reaction/${messageId}/like`, {
      messageId: messageId,
    });
  }

  /**
   * 取消点赞消息
   * @param messageId 消息ID
   */
  static async cancelLikeMessageFromApi(messageId: number): Promise<any> {
    return await api.delete(`api/message_reaction/${messageId}/like`);
  }

  /**
   * 不喜欢消息
   * @param messageId 消息ID
   */
  static async dislikeMessageFromApi(
    messageId: number,
    dislikeType: number,
    dislikeDesc: string
  ): Promise<any> {
    return await api.post(`api/message_reaction/${messageId}/dislike`, {
      messageId: messageId,
      dislike_category: dislikeType,
      dislike_comment: dislikeDesc,
    });
  }

  /**
   * 取消点赞消息
   * @param messageId 消息ID
   */
  static async cancelDislikeMessageFromApi(messageId: number): Promise<any> {
    return await api.delete(`api/message_reaction/${messageId}/dislike`);
  }
}

// 默认导出
export default ChatService;
