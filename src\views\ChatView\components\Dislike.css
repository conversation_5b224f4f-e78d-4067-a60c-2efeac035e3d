/* 不喜欢反馈对话框样式 */
.dislike-feedback-dialog {
  border-radius: 12px;
}

.dislike-feedback-dialog :deep(.el-dialog__header) {
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--el-border-color-light);
}

.dislike-feedback-dialog :deep(.el-dialog__title) {
  font-size: 18px;
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.dislike-feedback-dialog :deep(.el-dialog__body) {
  padding: 24px;
}

.dislike-feedback-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feedback-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--el-text-color-primary);
}

/* 类别按钮网格 */
.category-buttons {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1dvw;
}

.category-button {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 20px;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  cursor: pointer;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 140px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(135, 206, 235, 0.1);
}

.category-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(135, 206, 235, 0.1), rgba(173, 216, 230, 0.1));
  opacity: 0;
  transition: opacity 0.4s ease;
}

.category-button:hover {
  border-color: rgba(135, 206, 235, 0.5);
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 12px 40px rgba(135, 206, 235, 0.15);
}

.category-button:hover::before {
  opacity: 1;
}

.category-button.active {
  border-color: rgba(135, 206, 235, 0.7);
  background: rgba(135, 206, 235, 0.1);
  transform: translateY(-3px);
  box-shadow: 0 16px 48px rgba(135, 206, 235, 0.2);
}

.category-button.active::before {
  opacity: 1;
  background: linear-gradient(135deg, rgba(135, 206, 235, 0.15), rgba(173, 216, 230, 0.15));
}

.category-image {
  width: 100%;
  flex: 1;
  position: relative;
  z-index: 1;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4px 20px rgba(135, 206, 235, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.4s ease;
  filter: brightness(1.05) contrast(0.95) saturate(1.1);
}

.category-button:hover .category-image {
  transform: translateY(-1px);
  box-shadow: 0 6px 24px rgba(135, 206, 235, 0.15);
  border-color: rgba(135, 206, 235, 0.4);
}

.category-button:hover .category-image img {
  transform: scale(1.02);
  filter: brightness(1.1) contrast(0.9) saturate(1.2);
}

.category-button.active .category-image {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(135, 206, 235, 0.2);
  border-color: rgba(135, 206, 235, 0.6);
}

.category-button.active .category-image img {
  transform: scale(1.03);
  filter: brightness(1.15) contrast(0.85) saturate(1.3);
}

.category-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-primary);
  text-align: center;
  position: relative;
  z-index: 1;
  padding-block: 0.5dvw;
}

.category-button.active .category-label {
  color: var(--el-color-primary);
}

/* 描述输入区域 */
.description-section {
  margin-top: 8px;
}

.description-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 500;
  color: var(--el-text-color-regular);
}

.description-section :deep(.el-textarea__inner) {
  border-radius: 8px;
  border: 1px solid var(--el-border-color);
  transition: all 0.3s ease;
}

.description-section :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(96, 151, 252, 0.1);
}

/* 对话框底部按钮 */
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 16px 0 0 0;
}

.dialog-footer .el-button {
  padding: 10px 20px;
  border-radius: 8px;
  font-weight: 500;
}

.dialog-footer .el-button--primary {
  background: linear-gradient(135deg, var(--el-color-primary), #4f9cf9);
  border: none;
  box-shadow: 0 4px 12px rgba(96, 151, 252, 0.3);
  transition: all 0.3s ease;
}

.dialog-footer .el-button--primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 16px rgba(96, 151, 252, 0.4);
}

.dialog-footer .el-button--primary:disabled {
  background: var(--el-color-info-light-5);
  box-shadow: none;
  transform: none;
}
