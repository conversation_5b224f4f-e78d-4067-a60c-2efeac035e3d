import { AliyunSpeechTokenService } from '@/services/aliyunSpeechService';
import SpeechRecognitionFactory, {
    type ISpeechRecognition,
    type UnifiedSpeechConfig,
    type SpeechRecognitionResult
} from '@/utils/speechRecognitionFactory';
import AudioProcessor, { type AudioVolumeCallback } from '@/utils/audioProcessor';
import { SPEECH_RECOGNITION_CONFIG } from '@/constants/constant';
import { ElMessage } from 'element-plus';
import { defineStore } from 'pinia';
import { ref } from 'vue';

// 音频设备接口
export interface AudioDevice {
    deviceId: string;
    label: string;
    groupId: string;
}

// 阿里云Token响应接口
export interface TokenData {
    appKey: string;
    token: string;
    expireTime?: number;
}

export const useVoiceStore = defineStore('voice', () => {
    // 录音状态
    const isRecording = ref<boolean>(false);
    const audioDevices = ref<AudioDevice[]>([]);
    const selectedDeviceId = ref<string>('');
    const hasPermission = ref<boolean>(false);
    const isCheckingPermission = ref<boolean>(false);

    // 语音识别相关
    const speechRecognition = ref<ISpeechRecognition | null>(null);
    const audioProcessor = ref<AudioProcessor | null>(null);
    const recognitionText = ref<string>('');
    const isInitializingSpeech = ref<boolean>(false);
    const audioLevel = ref<number>(0); // 音频强度 0-1

    // 开始/停止录音
    const toggleRecording = async (): Promise<void> => {
        if (!isRecording.value) {
            await startRecording();
        } else {
            await stopRecording();
        }
    };

    // 开始录音（集成阿里云语音识别）
    const startRecording = async (): Promise<void> => {
        try {
            if (!hasPermission.value) {
                await requestMicrophonePermission();
            }

            if (!hasPermission.value) {
                ElMessage.error('需要麦克风权限才能录音');
                return;
            }

            isInitializingSpeech.value = true;
            ElMessage.info('正在初始化语音识别...');

            // 根据配置类型初始化语音识别
            if (SPEECH_RECOGNITION_CONFIG.TYPE === 'aliyun') {
                // 获取阿里云Token
                const tokenData = await AliyunSpeechTokenService.getAliyunToken();
                await initializeSpeechRecognition({
                    appKey: tokenData.appKey,
                    token: tokenData.token
                });
            } else {
                // 使用自定义语音识别服务
                await initializeSpeechRecognition({});
            }

            // 启动录音
            const constraints: MediaStreamConstraints = {
                audio: selectedDeviceId.value ? {
                    deviceId: { exact: selectedDeviceId.value }
                } : true
            };

            const stream = await navigator.mediaDevices.getUserMedia(constraints);

            // 初始化音频处理器
            audioProcessor.value = new AudioProcessor();
            await audioProcessor.value.initializeAudioContext();

            // 开始音频处理，添加音量监听
            const volumeCallback: AudioVolumeCallback = (volume: number) => {
                audioLevel.value = volume;
            };

            audioProcessor.value.startProcessing(stream, (audioData: ArrayBuffer) => {
                if (speechRecognition.value) {
                    speechRecognition.value.sendAudioData(audioData);
                }
            }, volumeCallback);

            // 启动语音识别
            await speechRecognition.value!.startRecognition();

            isRecording.value = true;
            isInitializingSpeech.value = false;
            recognitionText.value = '';
            ElMessage.success('开始录音和语音识别');

        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error('开始录音失败：', error);
            isInitializingSpeech.value = false;
            ElMessage.error(`开始录音失败：${errorMessage}`);
        }
    };

    // 停止录音
    const stopRecording = async (): Promise<void> => {
        if (!isRecording.value) return;

        try {
            // 立即停止音频处理
            if (audioProcessor.value) {
                audioProcessor.value.stopProcessing();
            }

            // 停止语音识别并立即断开WebSocket连接
            if (speechRecognition.value) {
                await speechRecognition.value.stopRecognition();
                // 立即断开WebSocket连接
                speechRecognition.value.disconnect();
            }

            isRecording.value = false;
            audioLevel.value = 0; // 重置音频强度
            ElMessage.success('录音结束');
        } catch (error: unknown) {
            const errorMessage = error instanceof Error ? error.message : String(error);
            console.error('停止录音失败：', error);
            ElMessage.error(`停止录音失败：${errorMessage}`);
        }
    };

    // 初始化语音识别
    const initializeSpeechRecognition = async (config: UnifiedSpeechConfig): Promise<void> => {
        // 使用工厂创建语音识别实例
        speechRecognition.value = SpeechRecognitionFactory.createSpeechRecognition();

        await speechRecognition.value.initialize(config, {
            onResult: (result: SpeechRecognitionResult) => {
                recognitionText.value = result.text;
            },
            onError: (error: Error) => {
                console.error('语音识别错误：', error);
                ElMessage.error('语音识别错误：' + error.message);
            },
            onConnect: () => {
                console.log('语音识别连接成功');
            },
            onDisconnect: () => {
                console.log('语音识别连接断开');
            }
        });

        await speechRecognition.value.connect();
    };

    // 请求麦克风权限
    const requestMicrophonePermission = async (): Promise<boolean> => {
        if (isCheckingPermission.value) {
            return hasPermission.value;
        }

        isCheckingPermission.value = true;

        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            // 立即停止流，我们只是为了获取权限
            stream.getTracks().forEach((track: MediaStreamTrack) => track.stop());

            hasPermission.value = true;

            // 权限获取成功后，重新获取设备列表
            await getAudioDevices();

            return true;
        } catch (error: unknown) {
            console.error('麦克风权限获取失败：', error);
            hasPermission.value = false;

            if (error instanceof Error) {
                if (error.name === 'NotAllowedError') {
                    ElMessage.error('请允许使用麦克风权限');
                } else if (error.name === 'NotFoundError') {
                    ElMessage.error('未找到麦克风设备');
                } else {
                    ElMessage.error('获取麦克风权限失败');
                }
            } else {
                ElMessage.error('获取麦克风权限失败');
            }

            return false;
        } finally {
            isCheckingPermission.value = false;
        }
    };

    // 获取音频设备列表
    const getAudioDevices = async (): Promise<void> => {
        try {
            const devices = await navigator.mediaDevices.enumerateDevices();
            const inputDevices = devices.filter((device: MediaDeviceInfo) => device.kind === 'audioinput');

            audioDevices.value = inputDevices.map((device: MediaDeviceInfo) => ({
                deviceId: device.deviceId,
                label: device.label || `麦克风 ${device.deviceId.slice(0, 8)}`,
                groupId: device.groupId
            }));
            // 如果没有选择设备且有可用设备，选择第一个并真正切换到该设备
            if (!selectedDeviceId.value && audioDevices.value.length > 0) {
                await switchToDevice(audioDevices.value[0].deviceId);
            }

        } catch (error: unknown) {
            console.error('获取音频设备失败：', error);
            ElMessage.error('获取音频设备失败');
        }
    };

    // 真正切换到指定的音频设备
    const switchToDevice = async (deviceId: string): Promise<void> => {
        try {
            // 更新选中的设备ID
            selectedDeviceId.value = deviceId;

            // 测试新设备是否可用，通过获取媒体流来验证
            const constraints: MediaStreamConstraints = {
                audio: {
                    deviceId: { exact: deviceId }
                }
            };

            // 获取新设备的媒体流进行测试
            const testStream = await navigator.mediaDevices.getUserMedia(constraints);

            // 测试成功，立即停止测试流
            testStream.getTracks().forEach((track: MediaStreamTrack) => track.stop());
        } catch (error: unknown) {
            console.error('切换音频设备失败：', error);
            const errorMessage = error instanceof Error ? error.message : String(error);
            ElMessage.error(`切换音频设备失败：${errorMessage}`);

            // 如果切换失败，回退到之前的设备或默认设备
            if (audioDevices.value.length > 0) {
                selectedDeviceId.value = audioDevices.value[0].deviceId;
            }
        }
    };

    // 切换音频设备
    const handleDeviceChange = async (deviceId: string): Promise<void> => {
        // 如果正在录音，提示用户并阻止切换
        if (isRecording.value) {
            ElMessage.warning('请停止录音后再切换设备');
            return;
        }

        try {
            // 真正切换到新的音频设备
            await switchToDevice(deviceId);

            const device = audioDevices.value.find((d: AudioDevice) => d.deviceId === deviceId);
            if (device) {
                ElMessage.success(`已切换到：${device.label}`);
            }
        } catch (error: unknown) {
            console.error('切换设备时发生错误：', error);
        }
    };

    // 打开麦克风设置（权限检查）
    const openMicrophoneSettings = async (): Promise<void> => {
        // 每次点击都检查权限
        if (!hasPermission.value) {
            await requestMicrophonePermission();
        } else {
            // 如果已有权限，刷新设备列表
            await getAudioDevices();
        }
    };

    // 初始化语音功能
    const initializeVoice = async (): Promise<void> => {
        // 检查浏览器支持
        const support = AudioProcessor.checkBrowserSupport();
        if (!support.supported) {
            ElMessage.error(support.message);
            return;
        }

        // 预加载Token（仅在使用阿里云时）
        if (SPEECH_RECOGNITION_CONFIG.TYPE === 'aliyun') {
            try {
                await AliyunSpeechTokenService.preloadToken();
            } catch (error) {
                console.warn('预加载Token失败:', error);
            }
        }

        // 初始化时检查权限和设备，并真正切换到默认设备
        const permissionGranted = await requestMicrophonePermission();

        // 如果权限获取成功且有可用设备，确保真正切换到选中的设备
        if (permissionGranted && selectedDeviceId.value && audioDevices.value.length > 0) {
            try {
                await switchToDevice(selectedDeviceId.value);
            } catch (error) {
                console.error('初始化切换音频设备失败：', error);
            }
        }
    };

    // 清理资源
    const cleanup = (): void => {
        // 如果正在录音，先停止录音
        if (isRecording.value) {
            // 立即停止音频处理
            if (audioProcessor.value) {
                audioProcessor.value.stopProcessing();
            }
            isRecording.value = false;
        }

        // 清理语音识别资源并立即断开WebSocket
        if (speechRecognition.value) {
            speechRecognition.value.disconnect();
            speechRecognition.value = null;
        }

        // 清理音频处理器
        if (audioProcessor.value) {
            audioProcessor.value.dispose();
            audioProcessor.value = null;
        }
    };

    return {
        // 状态
        isRecording,
        audioDevices,
        selectedDeviceId,
        hasPermission,
        isCheckingPermission,
        speechRecognition,
        audioProcessor,
        recognitionText,
        isInitializingSpeech,
        audioLevel,

        // 方法
        toggleRecording,
        startRecording,
        stopRecording,
        initializeSpeechRecognition,
        requestMicrophonePermission,
        getAudioDevices,
        switchToDevice,
        handleDeviceChange,
        openMicrophoneSettings,
        initializeVoice,
        cleanup
    };
});