<script lang="ts" setup>
import ImageDiagnosisIcon from "@/components/icons/ImageDiagnosisIcon.vue";
import LabReportIcon from "@/components/icons/LabReportIcon.vue";
import StopIcon from "@/components/icons/StopIcon.vue";
import SubmitArrowIcon from "@/components/icons/SubmitArrowIcon.vue";
import {
  ChatService,
  type ChatMessage,
  type ImageAttachment,
} from "@/services/chatService";
import { useChatStore } from "@/stores/chat";
import { useConversationStore } from "@/stores/conversation";
import { useSliderStore } from "@/stores/slider";
import { ElMessage } from "element-plus";
import { storeToRefs } from "pinia";
import { computed, ref } from "vue";
import UploadReport from "./UploadReport.vue";
import UploadResult from "./UploadResult.vue";
import ReviewImage from "./ReviewImage.vue";

// 全局Pina库
const chatStore = useChatStore();
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();

// 全局响应数据
const { uploadReportImages, uploadResultImages, messageList, isAiTyping } =
  storeToRefs(chatStore);
const { curConversation } = storeToRefs(sliderStore);

// 全局方法
const { addConversation } = conversationStore;

// 本组件的响应数据
const senderValue = ref("");
const showReviewImageDialog = ref(false);
const reviewImageData = ref<{
  title: string;
  base64: string;
  ocrText: string;
}>({
  title: "",
  base64: "",
  ocrText: "",
});
const uploadedImages = computed(() => [
  ...uploadResultImages.value,
  ...uploadReportImages.value,
]);
const showUploadResultDialog = ref(false);
const showUploadReportDialog = ref(false);

// 本组件的方法
function addErrorMessage(msg: string = "输出已中断，请重新发送消息") {
  const lastAiMessage = messageList.value
    .slice()
    .reverse()
    .find((msg) => msg.type === "ai");
  if (lastAiMessage) {
    lastAiMessage.loading = false;
    lastAiMessage.thinkLoading = false;
    if (!lastAiMessage.content) {
      lastAiMessage.content = msg;
    }
  }
}
const addMessage = (
  messageId: number,
  message: string,
  type: "user" | "ai" = "user",
  uploadResultImages?: ImageAttachment[],
  uploadReportImages?: ImageAttachment[]
) => {
  const newMessage: ChatMessage = {
    id: messageId,
    type,
    content: message,
    timestamp: Date.now(),
    loading: type === "ai",
    thinkLoading: type === "ai",
    uploadResultImages,
    uploadReportImages,
  };

  messageList.value.push(newMessage);
  return newMessage;
};
const updateMessage = (
  messageId: number,
  content: string,
  thinking?: string,
  finished?: boolean,
  thinkFinished?: boolean
) => {
  const messageIndex = messageList.value.findIndex(
    (msg) => msg.id === messageId
  );
  if (messageIndex !== -1) {
    const message = messageList.value[messageIndex];

    // 批量更新消息属性以减少响应式触发次数
    const updates: Partial<typeof message> = {};

    if (content !== message.content) {
      updates.content = content;
    }
    if (thinking !== undefined && thinking !== message.thinking) {
      updates.thinking = thinking;
    }
    if (finished !== undefined && message.loading !== !finished) {
      updates.loading = !finished;
    }
    if (
      thinkFinished !== undefined &&
      message.thinkLoading !== !thinkFinished
    ) {
      updates.thinkLoading = !thinkFinished;
    }

    if (Object.keys(updates).length > 0) {
      Object.assign(message, updates);
    }
  }
};
const handleSendMessage = async () => {
  const userMessage = senderValue.value.trim();
  const resultImages = uploadResultImages.value;
  const reportImages = uploadReportImages.value;
  let curConversationId;
  if (!userMessage && uploadedImages.value.length === 0) {
    ElMessage.warning("请输入消息内容或上传图片");
    return;
  }
  if (isAiTyping.value) {
    ElMessage.warning("AI正在回复中，请稍候...");
    return;
  }
  // 清空输入框和附件
  senderValue.value = "";
  uploadResultImages.value = [];
  uploadReportImages.value = [];
  if (!!curConversation.value) {
    curConversationId = curConversation.value.conversation_id;
  } else {
    let newConversation;
    newConversation = await addConversation(
      userMessage ? userMessage.slice(0, 20) : "图片问诊"
    );
    curConversationId = newConversation.conversation_id;
    curConversation.value = newConversation;
  }

  // 添加用户消息
  addMessage(
    Date.now() + Math.random(),
    userMessage || "根据上传的图片回答问题",
    "user",
    resultImages,
    reportImages
  );
  isAiTyping.value = true;
  try {
    await ChatService.sendMessageStream(
      {
        message: userMessage,
        uploadResultImages:
          resultImages && resultImages.length > 0 ? resultImages : undefined,
        uploadReportImages:
          reportImages && reportImages.length > 0 ? reportImages : undefined,
        conversationId: curConversationId,
      },
      (chunk) => {
        // 更新消息内容
        updateMessage(
          chunk.messageId,
          chunk.content,
          chunk.thinking,
          chunk.finished,
          chunk.thinkFinished
        );
      },
      (messageId) => {
        addMessage(messageId, "", "ai");
      }
    );
  } catch (error) {
    addErrorMessage("抱歉，我现在无法回复您的消息，请稍后再试。");
  } finally {
    isAiTyping.value = false;
  }
};

const handleStopResponse = () => {
  ChatService.abortCurrentRequest();
  isAiTyping.value = false;
  addErrorMessage("输出已中断");
};

const handleImageClick = (image: ImageAttachment) => {
  reviewImageData.value = {
    title: image.name,
    base64: image.response.base64,
    ocrText: image.response.ocrText || "",
  };
  showReviewImageDialog.value = true;
};

const handleDeleteReportImage = (imageUid: number) => {
  uploadReportImages.value = uploadReportImages.value.filter(
    (image) => image.uid !== imageUid
  );
};

const handleDeleteResultImage = (imageUid: number) => {
  uploadResultImages.value = uploadResultImages.value.filter(
    (image) => image.uid !== imageUid
  );
};

const handleUploadReportComplete = (files: any[]) => {
  if (files.length > 0) {
    uploadReportImages.value = [...uploadReportImages.value, ...files];
  }
};

const handleUploadResultComplete = (files: any[]) => {
  if (files.length > 0) {
    uploadResultImages.value = [...uploadResultImages.value, ...files];
  }
};
</script>

<template>
  <div class="chat-input">
    <div class="chat-input-content">
      <div v-if="uploadReportImages.length > 0 || uploadResultImages.length > 0" class="uploaded-image-container">
        <div class="uploaded-header">仅识别图像中的文字</div>
        <div class="uploaded-image-content">
          <div v-if="uploadResultImages.length > 0" class="uploaded-image-header">
            化验结果
          </div>
          <div v-if="uploadResultImages.length > 0" class="uploaded-image-list">
            <div v-for="(image, index) in uploadResultImages" :key="image.uid" class="image-item"
              @click="handleImageClick(image)">
              <el-image :src="image.response.base64" fit="cover" class="image-thumbnail" :initial-index="index" />
              <div class="image-info">
                <span class="image-name">{{ image.name }}</span>
                <span class="image-size">{{ (image.size / 1024).toFixed(1) }}KB</span>
              </div>
              <el-button class="image-delete-btn" size="small" type="danger" circle plain link
                @click.stop="handleDeleteResultImage(image.uid)">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
          </div>
          <div v-if="
            uploadReportImages.length > 0 && uploadResultImages.length > 0
          " style="height: 1dvw"></div>
          <div v-if="uploadReportImages.length > 0" class="uploaded-image-header">
            影像报告
          </div>
          <div v-if="uploadReportImages.length > 0" class="uploaded-image-list">
            <div v-for="(image, index) in uploadReportImages" :key="image.uid" class="image-item"
              @click="handleImageClick(image)">
              <el-image :src="image.response.base64" fit="cover" class="image-thumbnail" :initial-index="index" />
              <div class="image-info">
                <span class="image-name">{{ image.name }}</span>
                <span class="image-size">{{ (image.size / 1024).toFixed(1) }}KB</span>
              </div>
              <el-button class="image-delete-btn" size="small" type="danger" circle plain link
                @click.stop="handleDeleteReportImage(image.uid)">
                <el-icon>
                  <Close />
                </el-icon>
              </el-button>
            </div>
          </div>
        </div>
      </div>
      <MentionSender v-model="senderValue" @submit="handleSendMessage" variant="updown"
        :auto-size="{ minRows: 2, maxRows: 6 }">
        <template #action-list>
          <div style="display: flex; gap: 1dvw">
            <button class="chat-input-other-button" @click="showUploadResultDialog = true">
              <LabReportIcon />
              <span style="margin-left: 0.2dvw">化验结果</span>
            </button>
            <button class="chat-input-other-button" @click="showUploadReportDialog = true">
              <ImageDiagnosisIcon />
              <span style="margin-left: 0.2dvw">影像报告</span>
            </button>
          </div>
          <div class="chat-input-main-button">
            <!-- AI正在回复时显示停止按钮 -->
            <el-button @mousedown.stop v-if="isAiTyping" circle @click="handleStopResponse()">
              <el-icon class="stop-icon">
                <StopIcon />
              </el-icon>
            </el-button>
            <!-- AI未回复时显示发送按钮 -->
            <el-button @mousedown.stop v-else circle :disabled="(!senderValue.trim() &&
                !uploadedImages.some((item) => item.status === 'success')) ||
              uploadedImages.some((item) => item.status === 'uploading')
              " @click="handleSendMessage()">
              <el-icon class="submit-icon">
                <SubmitArrowIcon />
              </el-icon>
            </el-button>
          </div>
        </template>
      </MentionSender>
    </div>
    <div class="chat-input-remarks">内容由 AI 生成，请仔细甄别</div>
    <!-- 上传报告对话框 -->
    <UploadReport v-model="showUploadReportDialog" @complete="handleUploadReportComplete" />
    <!-- 上传报告对话框 -->
    <UploadResult v-model="showUploadResultDialog" @complete="handleUploadResultComplete" />
    <!-- 图片预览对话框 -->
    <ReviewImage v-model="showReviewImageDialog" :title="reviewImageData.title" :base64="reviewImageData.base64"
      :ocr-text="reviewImageData.ocrText" />
  </div>
</template>
<style scoped src="./ChatInput.css"></style>
