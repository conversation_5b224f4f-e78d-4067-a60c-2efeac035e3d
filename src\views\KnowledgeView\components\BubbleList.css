.bubble-list-container {
  width: 100%;
}

.bubble-list-container .el-bubble-list {
  height: 90dvh;
}

.bubble-list-container .el-bubble-list::after {
  content: "占位符";
  display: block;
  width: 100%;
  line-height: 25dvh;
  opacity: 0;
}

.bubble-list-container .el-bubble-list.bubble-list {
  padding-inline: 15%;
}

:deep(.bubble-list .el-bubble.el-bubble-end .el-bubble-avatar-placeholder) {
  display: none;
}

:deep(.bubble-list .el-avatar) {
  background: linear-gradient(315deg, #a0d8f0 0%, #80b3ff 100%);
}

:deep(
  .bubble-list
    .el-bubble.el-bubble-start
    .el-bubble-content.el-bubble-content-filled
) {
  width: 70%;
  max-width: 70%;
}

:deep(.bubble-list .el-bubble-footer) {
  width: 70%;
  max-width: 70%;
}

/* Footer 按钮样式 */
.footer-container {
  width: 100%;
}

.footer-book{
  color:var(--el-color-info);
}

.footer-book-item{
  display: flex;
  gap: 0.3dvw;
  align-items: center;
  margin-block: 0.1dvw;
}

:deep(.bubble-list .document-book.circled-number){
  background: var(--el-color-info-light-5);
  display: inline-flex;
  width: 0.85dvw;
  height: 0.85dvw;
  font-size: 0.55dvw;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  margin-inline: 0.2dvw;
}

/* Popover 弹出框样式 */
.document-popover {
  position: fixed;
  z-index: 10000;
  background: white;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-width: 300px;
  min-width: 200px;
  padding: 0;
  font-size: 14px;
  line-height: 1.5;
  transform: translateX(-50%) translateY(-100%);
  animation: popoverFadeIn 0.2s ease-out;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  will-change: transform;
}

.document-popover::before {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 0;
  height: 0;
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-top: 8px solid white;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.popover-header {
  background: linear-gradient(135deg, #6097fc 0%, #87ceeb 100%);
  color: white;
  padding: 8px 12px;
  border-radius: 8px 8px 0 0;
  font-weight: 600;
  font-size: 13px;
}

.popover-content {
  padding: 12px;
  color: #333;
  max-height: 200px;
  overflow-y: auto;
  overflow-x: hidden;
  word-wrap: break-word;
  white-space: pre-wrap;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
}

/* 为 Webkit 浏览器自定义滚动条 */
.popover-content::-webkit-scrollbar {
  width: 6px;
}

.popover-content::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.popover-content::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.popover-content::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}

/* 文档片段标签样式增强 */
:deep(.document-book.circled-number) {
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

:deep(.document-book.circled-number:hover) {
  box-shadow: 0 2px 6px rgba(96, 151, 252, 0.3);
}

@keyframes popoverFadeIn {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-100%) translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(-100%);
  }
}

.footer-remarks {
  font-size: 12px;
  color: rgb(255, 179, 0);
  display: flex;
  align-items: center;
}
