<script lang="ts" setup>
import Feedback from "@/components/common/Feedback.vue";
import LoginIcon from "@/components/icons/LoginIcon.vue";
import LogoutIcon from "@/components/icons/LogoutIcon.vue";
import { useCommonStore } from "@/stores/common";
import {
  ArrowLeft,
  ArrowRight,
  ChatDotRound,
  Loading,
  Plus,
} from "@element-plus/icons-vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { storeToRefs } from "pinia";
import { computed, onMounted, ref } from "vue";
import { useRouter } from "vue-router";
import { FUNCTION_LIST } from "../../constants/constant";
import { type ConversationInfo } from "../../services/conversationService";
import { useConversationStore } from "../../stores/conversation";
import { useSliderStore } from "../../stores/slider";
import DeleteIcon from "../icons/DeleteIcon.vue";
import EditIcon from "../icons/EditIcon.vue";
import MoreDotsIcon from "../icons/MoreDotsIcon.vue";

// 全局的Pina库
const sliderStore = useSliderStore();
const conversationStore = useConversationStore();
const commonStore = useCommonStore();

// 全局方法
const { clickFunction, clickHistory } = sliderStore;
const { getLoginUserInfo } = commonStore;
const {
  getConversationList,
  removeConversation,
  renameConversation,
  loadMoreConversationList,
} = conversationStore;

// 全局的响应数据
const { curFunction, curConversation, isCollapsed } = storeToRefs(sliderStore);
const { pagination, conversationList } = storeToRefs(conversationStore);
const { userInfo } = storeToRefs(commonStore);

// 全局方法
const { clickNewChat } = sliderStore;

// 本组件的初始化
onMounted(async () => {
  if (curFunction.value.id === "chat") {
    await getConversationList(1);
  } else if (curFunction.value.id === "voice") {
    await getConversationList(1, 3);
  }
  await getLoginUserInfo();
});

// 本组件的响应数据
const router = useRouter();
const renamingConversationId = ref<number | null>(null);
const renameInputValue = ref("");
const hasMore = computed(() => {
  const page = pagination.value.page;
  const pages = pagination.value.pages;
  return page && pages ? page < pages : true;
});
const showFeedbackDialog = ref(false);

// 本组件的方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
const handleMenuCommand = async (command: string, item: ConversationInfo) => {
  if (command === "rename") {
    startRename(item);
  } else if (command === "delete") {
    await deleteConversation(item);
  }
};
const startRename = (item: ConversationInfo) => {
  renamingConversationId.value = item.id;
  renameInputValue.value = item.title;
};
const cancelRename = () => {
  renamingConversationId.value = null;
  renameInputValue.value = "";
};
const confirmRename = async (item: ConversationInfo) => {
  const newTitle = renameInputValue.value.trim();

  if (!newTitle || newTitle === item.title) {
    cancelRename();
    return;
  }

  try {
    await renameConversation(item.id, newTitle);
  } finally {
    cancelRename();
  }
};
const deleteConversation = async (item: ConversationInfo) => {
  await ElMessageBox.confirm(
    `确定要删除对话"${item.title}"吗？删除后无法恢复。`,
    "确认删除",
    {
      confirmButtonText: "删除",
      cancelButtonText: "取消",
      type: "warning",
      confirmButtonClass: "el-button--danger",
    }
  );

  await removeConversation(item.id);
  if (curFunction.value.id === "chat") {
    await getConversationList(1);
  } else if (curFunction.value.id === "voice") {
    await getConversationList(1, 3);
  }
};
const loadMore = async () => {
  const curPage = pagination.value.page + 1;
  await loadMoreConversationList(curPage);
  const historyList = document.querySelector(".history-list");
  if (historyList) {
    historyList.scrollTop = historyList.scrollTop - 50;
  }
};
const handleFeedback = () => {
  showFeedbackDialog.value = true;
};
const goToHome = () => {
  router.push("/");
};
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm("确定要退出登录吗？", "退出确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });
    commonStore.logout();
    ElMessage.success("退出登录成功");
  } catch (error) {
    // 用户取消退出，不做任何操作
  }
};
const showLoginDialog = () => {
  commonStore.showLogin = true;
};
const handleCommand = (command: string) => {
  switch (command) {
    case "logout":
      handleLogout();
      break;
    case "login":
      showLoginDialog();
      break;
    case "feedback":
      handleFeedback();
      break;
    default:
      break;
  }
};
</script>

<template>
  <div class="slider" :class="{ collapsed: isCollapsed }">
    <div class="slider-logo">
      <img v-if="!isCollapsed" class="slider-logo-img" src="/text_logo_cn.png" alt="logo" @click="goToHome" />
      <img v-else class="slider-logo-img-collapsed" src="/logo.png" alt="logo" @click="goToHome" />
    </div>
    <!-- 功能模块区域 -->
    <div class="slider-function">
      <div v-if="!isCollapsed" class="section-header">
        <h3>功能模块</h3>
      </div>
      <ul class="function-list">
        <li v-for="item in FUNCTION_LIST" :key="item.id" @click="clickFunction(item)"
          :class="{ active: curFunction.id === item.id }" :title="isCollapsed ? item.title : ''">
          <div class="function-icon">
            <component :is="item.icon" />
          </div>
          <span v-if="!isCollapsed" class="function-text">{{
            item.title
          }}</span>
        </li>
      </ul>
    </div>

    <!-- 历史对话区域 -->
    <div v-if="!isCollapsed && curFunction.id !== 'knowledge'" class="slider-history">
      <div class="section-header">
        <h3>历史对话</h3>
        <el-icon @click="clickNewChat" class="new-chat-icon">
          <Plus />
        </el-icon>
      </div>
      <ul class="history-list" v-infinite-scroll="loadMore" :infinite-scroll-immediate="false"
        :infinite-scroll-disabled="!hasMore" :infinite-scroll-delay="2000">
        <li v-for="item in conversationList" :key="item.id" :class="{ active: curConversation?.id === item.id }"
          class="conversation-item" @click="clickHistory(item)">
          <div class="conversation-content">
            <!-- 重命名输入框 -->
            <el-input v-if="renamingConversationId === item.id" v-model="renameInputValue" size="small"
              @blur="cancelRename" @keyup.enter="confirmRename(item)" @keyup.esc="cancelRename" @click.stop
              class="rename-input" autofocus />
            <!-- 正常显示标题 -->
            <span v-else class="conversation-title">{{ item.title }}</span>
          </div>

          <!-- 三点菜单 -->
          <el-dropdown @command="(command: string) => handleMenuCommand(command, item)" trigger="click"
            placement="bottom-end" class="conversation-menu" @click.stop>
            <el-button type="text" size="small" class="menu-button" @click.stop>
              <MoreDotsIcon />
            </el-button>
            <template #dropdown>
              <el-dropdown-menu class="conversation-dropdown-menu">
                <el-dropdown-item command="rename" class="rename-item">
                  <EditIcon />
                  <span style="margin-left: 0.3dvw; color: #f56c6c">重命名</span>
                </el-dropdown-item>
                <el-dropdown-item command="delete" class="delete-item">
                  <DeleteIcon />
                  <span style="margin-left: 0.3dvw">删除</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </li>
        <!-- 加载图标 -->
        <div v-if="hasMore" class="loading-container">
          下拉加载更多
          <el-icon class="loading-icon">
            <Loading />
          </el-icon>
        </div>
        <div v-else class="loading-container">暂无更多数据</div>
      </ul>
    </div>

    <!-- 底部区域 -->
    <div class="slider-footer">
      <div class="app-header-user">
        <!-- 用户头像下拉菜单（登录和未登录状态都显示） -->
        <el-dropdown @command="handleCommand" trigger="click" placement="bottom-end" class="user-dropdown">
          <div class="app-user-info">
            <el-avatar src="https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png" class="user-avatar" />
            <span v-if="!isCollapsed">{{ userInfo?.username }}</span>
          </div>
          <template #dropdown>
            <el-dropdown-menu class="user-dropdown-menu">
              <el-dropdown-item v-if="commonStore.isLogin" command="feedback" class="menu-item">
                <ChatDotRound />
                <span style="margin-left: 0.5dvw">意见反馈</span>
              </el-dropdown-item>
              <el-dropdown-item v-if="commonStore.isLogin" command="logout" class="menu-item">
                <LogoutIcon />
                <span style="margin-left: 0.5dvw">退出登录</span>
              </el-dropdown-item>
              <el-dropdown-item v-else command="login" class="menu-item">
                <LoginIcon />
                <span style="margin-left: 0.5dvw">登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 折叠/展开按钮 -->
      <el-button type="text" @click="toggleCollapse" class="collapse-button" :title="isCollapsed ? '展开侧边栏' : '折叠侧边栏'">
        <el-icon>
          <component :is="isCollapsed ? ArrowRight : ArrowLeft" />
        </el-icon>
      </el-button>
    </div>
    <Feedback v-model="showFeedbackDialog" />
  </div>
</template>

<style scoped src="./AppSlider.css"></style>
