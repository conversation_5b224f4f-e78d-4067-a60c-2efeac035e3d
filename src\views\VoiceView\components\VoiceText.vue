<script setup lang="ts">
import { useMedicalRecordStore } from '@/stores/medicalRecord';
import { useVoiceStore } from '@/stores/voice';
import { storeToRefs } from 'pinia';
import { ref } from 'vue';

interface Emits {
    (e: "send-message", voiceMedicalRecordId: number): void;
}
const emit = defineEmits<Emits>();

// 全局的Pina库
const voiceStore = useVoiceStore();
const medicalRecordStore = useMedicalRecordStore();

// 全局的响应数据
const {
    isRecording,
    recognitionText
} = storeToRefs(voiceStore);
const {
    medicalRecord
} = storeToRefs(medicalRecordStore);

// 全局的方法
const {
    createVoiceMedicalRecord
} = medicalRecordStore;

// 本组件的响应数据
const isEditing = ref(false);

// 本组件的方法
const onStartEdit = () => {
    if (!isRecording.value) {
        isEditing.value = true;
    }
};
const onFinishEdit = () => {
    isEditing.value = false;
};
const onCancelCreate = async () => {
    recognitionText.value = ""
    isEditing.value = false;
}
const onCreate = async () => {
    const text = recognitionText.value.trim()
    const result = await createVoiceMedicalRecord(text);
    medicalRecord.value = result
    emit('send-message', result.id!)
    recognitionText.value = ""
}
</script>

<template>
    <div class="voice-text-container">
        <el-card class="voice-text-card" body-class="voice-text-card-body" footer-class="voice-text-card-footer">
            <template #header>
                <div class="card-header">
                    <span>语音识别</span>
                </div>
            </template>
            <div class="text-content" @click="onStartEdit" v-if="!isEditing">
                <span v-if="recognitionText" class="text-display">{{ recognitionText }}</span>
                <span v-else class="text-placeholder">点击此处输入或编辑文本...</span>
            </div>
            <el-input v-else v-model="recognitionText" type="textarea" autosize placeholder="请输入文本..."
                @blur="onFinishEdit" @keydown.enter.ctrl="onFinishEdit" autofocus class="text-editor" />
            <template #footer>
                <el-button type="success" plain :disabled="isRecording" @click="onCreate">
                    创建病历
                </el-button>
                <el-button type="danger" plain @click="onCancelCreate" :disabled="isRecording">
                    取消创建
                </el-button>
            </template>
        </el-card>
    </div>
</template>

<style scoped src="./VoiceText.css"></style>