<script setup lang="ts">
import type { ImageAttachment } from "@/services/chatService";
import { OcrService } from "@/services/ocrService";
import { extractTablesFromMarkdown, extractNonTableContent, parseMarkdownTable, reconstructMarkdownTable, type TableData } from "@/utils/markdownUtils";
import { type UploadRequestOptions } from "element-plus";
import MarkdownIt from "markdown-it";
import { computed, ref } from "vue";
import EditableTable from "./EditableTable.vue";

// Props
interface Props {
  modelValue: boolean;
}

// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
  (e: "complete", files: ImageAttachment[]): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 响应式数据
const ImageAttachments = ref<ImageAttachment[]>([]);
const curFile = ref<ImageAttachment>();
const loadingUids = ref<number[]>([]);

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

const loading = computed((): boolean => {
  return !!(loadingUids.value && loadingUids.value?.length > 0);
});

const successFiles = computed(() => {
  return ImageAttachments.value.filter((file) => file.status === "success");
});

const showFile = computed(() => {
  return curFile.value || successFiles.value[0] || null;
});

const handleUpload = async (options: UploadRequestOptions): Promise<any> => {
  const { file, onSuccess, onError } = options;
  let data: any = {};
  try {
    // 添加到loading列表
    loadingUids.value.push(file.uid);
    const base64 = await OcrService.fileToBase64(file);
    data.base64 = base64;
    const response = await OcrService.ocrText(base64);
    onSuccess(response.result);
    // 保存完整的OCR文本
    data.ocrText = response.result?.text || '';
    // 分别提取表格和非表格内容
    data.tableContent = extractTablesFromMarkdown(response.result?.text || '');
    data.nonTableContent = extractNonTableContent(response.result?.text || '');
  } catch (err: any) {
    onError(err);
  } finally {
    // 从loading列表中移除
    loadingUids.value = loadingUids.value.filter((uid) => uid !== file.uid);
    return data;
  }
};

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
  ImageAttachments.value = [];
  loadingUids.value = [];
};

// 完成上传
const handleComplete = () => {
  emit("complete", successFiles.value);
  handleClose();
};

// 选择文件显示OCR文本
const handleSelectFile = (file: ImageAttachment) => {
  curFile.value = file;
};

// 预览图片（放大显示）
const handlePreviewImage = (file: ImageAttachment) => {
  // 创建预览容器
  const previewContainer = document.createElement("div");
  previewContainer.style.cssText = `
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    cursor: pointer;
  `;

  const previewImg = document.createElement("img");
  previewImg.src = file.response.base64;
  previewImg.style.cssText = `
    max-width: 90%;
    max-height: 90%;
    object-fit: contain;
  `;

  previewContainer.appendChild(previewImg);
  document.body.appendChild(previewContainer);

  // 点击关闭预览
  previewContainer.addEventListener("click", () => {
    document.body.removeChild(previewContainer);
  });
};

// 解析表格数据
const tableData = computed((): TableData[] => {
  if (!showFile.value?.response?.tableContent) return [];
  return parseMarkdownTable(showFile.value.response.tableContent);
});

// 检查是否有表格数据
const hasTableData = computed(() => tableData.value.length > 0);

// 检查是否有非表格内容（宠物信息等）
const hasNonTableContent = computed(() => {
  return !!(showFile.value?.response?.nonTableContent && showFile.value.response.nonTableContent.trim().length > 0);
});

// 用于非表格内容的渲染（宠物信息等）
const parsedNonTableContent = computed(() => {
  if (!showFile.value?.response?.nonTableContent) return null;
  const md = new MarkdownIt();
  return md.render(showFile.value.response.nonTableContent);
});

// 用于完整OCR内容的渲染（备用）
const parsedOcrText = computed(() => {
  if (!showFile.value?.response?.ocrText) return null;
  const md = new MarkdownIt();
  return md.render(showFile.value.response.ocrText);
});

// 更新表格数据
const handleTableUpdate = (updatedMarkdown: string, tableIndex?: number) => {
  if (!showFile.value) return;

  let finalTableMarkdown = updatedMarkdown;

  // 如果有多个表格，需要重新组合所有表格
  if (tableData.value.length > 1 && typeof tableIndex === 'number') {
    const updatedTables = [...tableData.value];
    // 这里需要解析更新后的markdown并替换对应的表格
    const parsedUpdatedTable = parseMarkdownTable(updatedMarkdown);
    if (parsedUpdatedTable.length > 0) {
      updatedTables[tableIndex] = parsedUpdatedTable[0];
    }

    // 重新组合所有表格为完整的markdown
    finalTableMarkdown = updatedTables
      .map(table => reconstructMarkdownTable(table))
      .join('\n\n');
  }

  // 更新当前文件的表格内容
  showFile.value.response.tableContent = finalTableMarkdown;

  // 重新组合完整的OCR文本（表格 + 非表格内容）
  const nonTableContent = showFile.value.response.nonTableContent || '';
  const combinedContent = [nonTableContent, finalTableMarkdown]
    .filter(content => content.trim().length > 0)
    .join('\n\n');
  showFile.value.response.ocrText = combinedContent;

  // 同时更新ImageAttachments中对应的文件
  const fileIndex = ImageAttachments.value.findIndex(
    file => file.uid === showFile.value?.uid
  );

  if (fileIndex !== -1) {
    ImageAttachments.value[fileIndex].response.tableContent = finalTableMarkdown;
    ImageAttachments.value[fileIndex].response.ocrText = combinedContent;
  }
};
</script>

<template>
  <el-dialog v-model="dialogVisible" title="化验结果上传" width="70%" :before-close="handleClose" top="3vh"
    style="margin-bottom: 0" center>
    <div class="upload-result-dialog">
      <div class="upload-section">
        <div class="upload-content" v-if="successFiles && successFiles.length > 0">
          <!-- 宠物信息等非表格内容 -->
          <div v-if="hasNonTableContent" class="pet-info-section">
            <div class="section-title">宠物信息</div>
            <div v-html="parsedNonTableContent" class="markdown-content pet-info-content"></div>
          </div>

          <!-- 表格数据 -->
          <div v-if="hasTableData" class="table-section">
            <div class="section-title">化验结果</div>
            <div class="table-container">
              <EditableTable v-for="(table, index) in tableData" :key="`table-${index}`" :table-data="table"
                @update="(markdown: any) => handleTableUpdate(markdown, index)" class="editable-table-wrapper" />
            </div>
          </div>

          <!-- 如果既没有表格也没有宠物信息，显示原始内容 -->
          <div v-if="!hasTableData && !hasNonTableContent" v-html="parsedOcrText" class="markdown-content"></div>
        </div>
        <div v-else style="height: 50dvh; text-align: center">
          <img style="height: 100%; border-radius: 5dvw" src="/chat/no_upload_result_files.jpeg" />
        </div>
        <el-upload drag multiple v-model:file-list="ImageAttachments" :http-request="handleUpload" :disabled="loading"
          :show-file-list="false">
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            <template v-if="loading">
              正在上传 {{ loadingUids.length }} 个文件，请稍候...
            </template>
            <template v-else>
              拖动化验结果文件到此处 或 <em>点击选择文件</em>
            </template>
          </div>
        </el-upload>
      </div>
      <div class="upload-slider" v-if="successFiles && successFiles.length > 0">
        <ul class="upload-file-list">
          <li v-for="file in successFiles" :key="file.id" class="upload-file-item"
            :class="{ active: curFile?.uid === file.uid }" @click="handleSelectFile(file)">
            <div class="upload-file-image-container">
              <el-image :src="file.response.base64" fit="cover" class="upload-file-image" />
              <el-button class="expand-button" size="small" circle @click.stop="handlePreviewImage(file)">
                <el-icon><zoom-in /></el-icon>
              </el-button>
            </div>
            <el-text class="upload-file-title" truncated>
              {{ file.name }}
            </el-text>
          </li>
        </ul>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose" class="cancel-button"> 取消 </el-button>
        <el-button type="primary" @click="handleComplete" class="complete-button" :loading="loading">
          完成 ({{
            ImageAttachments.filter((f) => f.status === "success").length
          }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./UploadResult.css"></style>
