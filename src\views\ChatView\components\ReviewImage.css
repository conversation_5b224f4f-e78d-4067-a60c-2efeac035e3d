.review-container {
  display: flex;
  height: 80dvh;
  gap: 24px;
}

.review-image {
  width: 50%;
  -ms-overflow-style: none;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
  padding: 24px;
  border-radius: 12px;
}

.upload-image-card {
  background-color: white;
  overflow: auto;
  height: 100%;
}

.upload-content {
  width: 50%;
  -ms-overflow-style: none;
  background: rgb(15, 57, 57);
  border-radius: 12px;
  padding: 24px;
}

.upload-content-card {
  background-color: white;
  overflow: auto;
  height: 100%;
}
