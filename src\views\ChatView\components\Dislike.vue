<script setup lang="ts">
import { DISLIKE_CATEGORIES } from "@/constants/constant";
import { useChatStore } from "@/stores/chat";
import { ElMessage } from "element-plus";
import { ref, watch } from "vue";

interface Props {
  visible: boolean;
  messageId: number | null;
}

const props = defineProps<Props>();
const emit = defineEmits<{
  "update:visible": [value: boolean];
  close: [];
}>();

const chatStore = useChatStore();

// 不喜欢反馈对话框相关状态
const selectedDislikeType = ref<number | null>(null);
const dislikeDescription = ref("");

// 使用常量中的不喜欢类别选项
const dislikeCategories = DISLIKE_CATEGORIES;

// 监听对话框显示状态，重置表单
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      selectedDislikeType.value = null;
      dislikeDescription.value = "";
    }
  }
);

// 选择不喜欢类别
const selectDislikeType = (type: number) => {
  selectedDislikeType.value = type;
};

// 提交不喜欢反馈
const submitDislikeFeedback = async () => {
  if (!selectedDislikeType.value || !props.messageId) {
    ElMessage.warning("请选择一个反馈类别");
    return;
  }

  try {
    await chatStore.dislikeMessage(
      props.messageId,
      selectedDislikeType.value,
      dislikeDescription.value
    );
    ElMessage.success("感谢您的反馈！");
    emit("update:visible", false);
    emit("close");
  } catch (error) {
    ElMessage.error("提交反馈失败，请稍后重试");
  }
};

// 取消不喜欢反馈对话框
const cancelDislikeFeedback = () => {
  emit("update:visible", false);
  emit("close");
  selectedDislikeType.value = null;
  dislikeDescription.value = "";
};
</script>

<template>
  <!-- 不喜欢反馈对话框 -->
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="反馈"
    width="40dvw"
    :before-close="cancelDislikeFeedback"
    class="dislike-feedback-dialog"
  >
    <div class="dislike-feedback-content">
      <!-- 不喜欢类别选择按钮 -->
      <div class="category-buttons">
        <div
          v-for="category in dislikeCategories"
          :key="category.type"
          class="category-button"
          :class="{ active: selectedDislikeType === category.type }"
          @click="selectDislikeType(category.type)"
        >
          <div class="category-image">
            <img :src="category.image" :alt="category.label" />
          </div>
          <div class="category-label">{{ category.label }}</div>
        </div>
      </div>

      <!-- 详细描述输入框 -->
      <div class="description-section">
        <p class="description-title">详细描述（可选）：</p>
        <el-input
          v-model="dislikeDescription"
          type="textarea"
          :rows="3"
          placeholder="请描述具体问题或建议..."
          maxlength="500"
          show-word-limit
        />
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelDislikeFeedback">取消</el-button>
        <el-button
          type="primary"
          @click="submitDislikeFeedback"
          :disabled="!selectedDislikeType"
        >
          反馈
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<style scoped src="./Dislike.css"></style>
