<script setup lang="ts">
import MarkdownIt from "markdown-it";
import { computed } from "vue";

interface Props {
  title: string;
  base64: string;
  ocrText: string;
  modelValue: boolean;
}
// Emits
interface Emits {
  (e: "update:modelValue", value: boolean): void;
}
const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit("update:modelValue", value),
});

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false;
};

const parsedOcrText = computed(() => {
  const md = new MarkdownIt();
  return md.render(props.ocrText);
});
</script>

<template>
  <el-dialog
    v-model="dialogVisible"
    :title="title"
    width="70%"
    :before-close="handleClose"
    top="3vh"
    style="margin-bottom: 0"
    center
  >
    <div class="review-container">
      <div class="review-image">
        <div class="upload-image-card">
            <img :src="base64" style="width: 100%;" />
        </div>
      </div>
      <div class="upload-content">
        <div class="upload-content-card">
          <div style="padding: 12px 24px 24px" v-html="parsedOcrText"></div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<style scoped src="./ReviewImage.css"></style>
