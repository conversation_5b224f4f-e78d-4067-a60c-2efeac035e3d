import VoiceService, { type AliyunTokenResponse } from "./voiceService";
import { SPEECH_RECOGNITION_CONFIG } from '@/constants/constant';

/**
 * 阿里云语音服务Token管理
 */
export class AliyunSpeechTokenService {
    private static tokenCache: AliyunTokenResponse | null = null;
    private static readonly TOKEN_CACHE_KEY = SPEECH_RECOGNITION_CONFIG.ALIYUN.TOKEN_CACHE.CACHE_KEY;
    private static readonly BUFFER_TIME = SPEECH_RECOGNITION_CONFIG.ALIYUN.TOKEN_CACHE.BUFFER_TIME;

    /**
     * 获取阿里云语音识别Token
     * @returns Promise<AliyunTokenResponse>
     */
    static async getAliyunToken(): Promise<AliyunTokenResponse> {
        try {
            // 检查缓存的Token是否有效
            const cachedToken = this.getCachedToken();
            if (cachedToken && this.isTokenValid(cachedToken)) {
                console.log('[AliyunToken] 使用缓存的Token');
                return cachedToken;
            }
            // 从服务器获取新Token
            const response = await VoiceService.getAliyunToken();
            if (!response.token || !response.appKey) {
                throw new Error('服务器返回的Token信息不完整');
            }
            // 缓存Token
            this.cacheToken(response);
            return response;

        } catch (error: any) {
            console.error('[AliyunToken] Token获取失败:', error);
            // 如果有缓存的Token，尝试使用（即使可能过期）
            const cachedToken = this.getCachedToken();
            if (cachedToken) {
                console.warn('[AliyunToken] 使用可能过期的缓存Token');
                return cachedToken;
            }
            throw new Error('无法获取阿里云语音识别Token: ' + (error.message || error));
        }
    }

    /**
     * 预加载Token（可用于页面初始化时预先获取）
     */
    static async preloadToken(): Promise<void> {
        try {
            await this.getAliyunToken();
            console.log('[AliyunToken] Token预加载完成');
        } catch (error) {
            console.warn('[AliyunToken] Token预加载失败:', error);
        }
    }

    /**
     * 获取缓存的Token
     */
    private static getCachedToken(): AliyunTokenResponse | null {
        try {
            // 优先从内存缓存获取
            if (this.tokenCache) {
                return this.tokenCache;
            }
            // 从本地存储获取
            const cached = localStorage.getItem(this.TOKEN_CACHE_KEY);
            if (cached) {
                const tokenData = JSON.parse(cached);
                this.tokenCache = tokenData;
                return tokenData;
            }
        } catch (error) {
            console.warn('[AliyunToken] 读取缓存Token失败:', error);
        }

        return null;
    }

    /**
     * 缓存Token
     */
    private static cacheToken(tokenData: AliyunTokenResponse): void {
        try {
            this.tokenCache = tokenData;
            localStorage.setItem(this.TOKEN_CACHE_KEY, JSON.stringify(tokenData));
        } catch (error) {
            console.warn('[AliyunToken] 缓存Token失败:', error);
        }
    }

    /**
     * 检查Token是否有效
     */
    private static isTokenValid(tokenData: AliyunTokenResponse): boolean {
        if (!tokenData.expireTime) {
            return false;
        }

        const now = Date.now();
        const expireTime = tokenData.expireTime;

        // 如果Token在缓冲时间内即将过期，则认为无效
        return now < (expireTime - this.BUFFER_TIME);
    }

    /**
     * 清除Token缓存
     */
    static clearTokenCache(): void {
        this.tokenCache = null;
        try {
            localStorage.removeItem(this.TOKEN_CACHE_KEY);
            console.log('[AliyunToken] Token缓存已清除');
        } catch (error) {
            console.warn('[AliyunToken] 清除Token缓存失败:', error);
        }
    }

    /**
     * 强制刷新Token
     */
    static async refreshToken(): Promise<AliyunTokenResponse> {
        this.clearTokenCache();
        return await this.getAliyunToken();
    }
}

export default AliyunSpeechTokenService;